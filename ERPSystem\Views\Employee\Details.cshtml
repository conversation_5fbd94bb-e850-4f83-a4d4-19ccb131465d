@model ERPSystem.Models.Entities.Employee
@{
    ViewData["Title"] = "تفاصيل المنتسب - " + Model.FullName;
}

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="page-title">
                    <i class="fas fa-user text-primary"></i>
                    تفاصيل المنتسب
                </h2>
                <div class="page-actions">
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                        <i class="fas fa-edit"></i>
                        تعديل البيانات
                    </a>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Employee Information -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user"></i>
                        البيانات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الاسم الكامل</label>
                            <p class="form-control-plaintext">@Model.FullName</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الرقم الوطني</label>
                            <p class="form-control-plaintext">@Model.NationalId</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">نوع المنتسب</label>
                            <p class="form-control-plaintext">
                                @switch (Model.EmployeeType)
                                {
                                    case ERPSystem.Models.Entities.EmployeeType.Officer:
                                        <span class="badge bg-primary fs-6">ضابط</span>
                                        break;
                                    case ERPSystem.Models.Entities.EmployeeType.NCO:
                                        <span class="badge bg-success fs-6">ضابط صف</span>
                                        break;
                                    case ERPSystem.Models.Entities.EmployeeType.Personnel:
                                        <span class="badge bg-info fs-6">فرد</span>
                                        break;
                                    case ERPSystem.Models.Entities.EmployeeType.Employee:
                                        <span class="badge bg-warning fs-6">موظف مدني</span>
                                        break;
                                }
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">نوع التوظيف</label>
                            <p class="form-control-plaintext">
                                @if (Model.EmploymentType.HasValue)
                                {
                                    @(Model.EmploymentType == ERPSystem.Models.Entities.EmploymentType.Appointment ? "تعيين" : "تعاقد")
                                }
                                else
                                {
                                    <span class="text-muted">غير محدد</span>
                                }
                            </p>
                        </div>
                        @if (!string.IsNullOrEmpty(Model.EmployeeNumber))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">رقم الموظف</label>
                                <p class="form-control-plaintext">@Model.EmployeeNumber</p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.MilitaryNumber))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الرقم العسكري</label>
                                <p class="form-control-plaintext">@Model.MilitaryNumber</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Job Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-briefcase"></i>
                        بيانات العمل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if (!string.IsNullOrEmpty(Model.Rank))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الرتبة</label>
                                <p class="form-control-plaintext">@Model.Rank</p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.Position))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">المنصب</label>
                                <p class="form-control-plaintext">@Model.Position</p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.Department))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">القسم</label>
                                <p class="form-control-plaintext">@Model.Department</p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.Unit))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الوحدة</label>
                                <p class="form-control-plaintext">@Model.Unit</p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.SubUnit))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الوحدة الفرعية</label>
                                <p class="form-control-plaintext">@Model.SubUnit</p>
                            </div>
                        }
                        @if (Model.HireDate.HasValue)
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ التوظيف</label>
                                <p class="form-control-plaintext">@Model.HireDate.Value.ToString("dd/MM/yyyy")</p>
                            </div>
                        }
                        @if (Model.Office != null)
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">المكتب</label>
                                <p class="form-control-plaintext">@Model.Office.Name</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Personal Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-id-card"></i>
                        البيانات الشخصية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if (Model.DateOfBirth.HasValue)
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ الميلاد</label>
                                <p class="form-control-plaintext">@Model.DateOfBirth.Value.ToString("dd/MM/yyyy")</p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.PlaceOfBirth))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">مكان الميلاد</label>
                                <p class="form-control-plaintext">@Model.PlaceOfBirth</p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.Nationality))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الجنسية</label>
                                <p class="form-control-plaintext">@Model.Nationality</p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.Religion))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الديانة</label>
                                <p class="form-control-plaintext">@Model.Religion</p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.MaritalStatus))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الحالة الاجتماعية</label>
                                <p class="form-control-plaintext">@Model.MaritalStatus</p>
                            </div>
                        }
                        @if (Model.NumberOfChildren.HasValue)
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">عدد الأطفال</label>
                                <p class="form-control-plaintext">@Model.NumberOfChildren</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-phone"></i>
                        بيانات الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @if (!string.IsNullOrEmpty(Model.Phone))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">رقم الهاتف</label>
                                <p class="form-control-plaintext">
                                    <a href="tel:@Model.Phone" class="text-decoration-none">@Model.Phone</a>
                                </p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.Email))
                        {
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">البريد الإلكتروني</label>
                                <p class="form-control-plaintext">
                                    <a href="mailto:@Model.Email" class="text-decoration-none">@Model.Email</a>
                                </p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.Address))
                        {
                            <div class="col-12 mb-3">
                                <label class="form-label fw-bold">العنوان</label>
                                <p class="form-control-plaintext">@Model.Address</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Side Panel -->
        <div class="col-lg-4">
            <!-- Employee Avatar -->
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div class="employee-avatar-large mb-3">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5 class="card-title">@Model.FullName</h5>
                    <p class="text-muted">@Model.NationalId</p>
                    <div class="employee-status">
                        @if (Model.IsActive)
                        {
                            <span class="badge bg-success fs-6">نشط</span>
                        }
                        else
                        {
                            <span class="badge bg-danger fs-6">غير نشط</span>
                        }
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i>
                        إحصائيات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="stat-item">
                                <h4 class="text-primary">@Model.Records.Count</h4>
                                <small class="text-muted">إجمالي السجلات</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stat-item">
                                <h4 class="text-success">@Model.Records.OfType<ERPSystem.Models.Entities.Promotion>().Count()</h4>
                                <small class="text-muted">الترقيات</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stat-item">
                                <h4 class="text-info">@Model.Records.OfType<ERPSystem.Models.Entities.Transfer>().Count()</h4>
                                <small class="text-muted">النقل</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="stat-item">
                                <h4 class="text-warning">@Model.Records.OfType<ERPSystem.Models.Entities.Leave>().Count()</h4>
                                <small class="text-muted">الإجازات</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="addRecord('promotion')">
                            <i class="fas fa-arrow-up"></i>
                            إضافة ترقية
                        </button>
                        <button class="btn btn-outline-success" onclick="addRecord('transfer')">
                            <i class="fas fa-exchange-alt"></i>
                            إضافة نقل
                        </button>
                        <button class="btn btn-outline-info" onclick="addRecord('leave')">
                            <i class="fas fa-calendar-alt"></i>
                            إضافة إجازة
                        </button>
                        <button class="btn btn-outline-warning" onclick="addRecord('appreciation')">
                            <i class="fas fa-award"></i>
                            إضافة تقدير
                        </button>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i>
                        معلومات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">تاريخ الإنشاء:</small>
                        <br>
                        <span>@Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</span>
                    </div>
                    @if (Model.UpdatedDate.HasValue)
                    {
                        <div class="mb-2">
                            <small class="text-muted">آخر تحديث:</small>
                            <br>
                            <span>@Model.UpdatedDate.Value.ToString("dd/MM/yyyy HH:mm")</span>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function addRecord(type) {
            // This will be implemented later when we create record management
            alert('سيتم تطوير هذه الميزة قريباً');
        }
    </script>
}

@section Styles {
    <style>
        .employee-avatar-large {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            margin: 0 auto;
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
        }

        .stat-item h4 {
            margin-bottom: 0.25rem;
            font-weight: 700;
        }

        .page-title {
            color: #1e40af;
            font-weight: 700;
        }

        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
            border-radius: 12px 12px 0 0 !important;
        }

        .form-control-plaintext {
            padding: 0.375rem 0;
            margin-bottom: 0;
            font-size: 1rem;
            line-height: 1.5;
            color: #212529;
            background-color: transparent;
            border: solid transparent;
            border-width: 1px 0;
        }

        .badge.fs-6 {
            font-size: 0.875rem !important;
            padding: 0.5rem 0.75rem;
        }
    </style>
}
