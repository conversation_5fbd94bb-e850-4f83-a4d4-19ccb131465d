using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using ERPSystem.Data;
using ERPSystem.Models.Entities;
using ERPSystem.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services.AddIdentity<ApplicationUser, ApplicationRole>(options =>
{
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = true;
    options.Password.RequiredLength = 6;
    options.Password.RequiredUniqueChars = 1;

    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;

    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
    options.User.RequireUniqueEmail = true;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders()
.AddClaimsPrincipalFactory<CustomUserClaimsPrincipalFactory>();

builder.Services.ConfigureApplicationCookie(options =>
{
    options.LoginPath = "/Account/Login";
    options.LogoutPath = "/Account/Logout";
    options.AccessDeniedPath = "/Account/AccessDenied";
});

// Add custom services
builder.Services.AddScoped<IAuthorizationService, AuthorizationService>();
builder.Services.AddScoped<IOrganizationalPermissionService, OrganizationalPermissionService>();

builder.Services.AddControllersWithViews();

var app = builder.Build();

// Seed database
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var context = services.GetRequiredService<ApplicationDbContext>();
        var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
        var roleManager = services.GetRequiredService<RoleManager<ApplicationRole>>();

        await SeedData(context, userManager, roleManager);
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while seeding the database.");
    }
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapStaticAssets();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}")
    .WithStaticAssets();

app.Run();

static async Task SeedData(ApplicationDbContext context, UserManager<ApplicationUser> userManager, RoleManager<ApplicationRole> roleManager)
{
    // Ensure database is created
    await context.Database.EnsureCreatedAsync();

    // Create roles with Arabic names
    string[] roles = {
        "المدير_العام",
        "المدير_الإداري",
        "شؤون_الضباط",
        "شؤون_ضباط_الصف",
        "شؤون_الموظفين",
        "الشؤون_العامة",
        "الكشوف_الشهرية",
        "المراسلات",
        "مدير_قسم",
        "مستخدم"
    };

    foreach (string role in roles)
    {
        if (!await roleManager.RoleExistsAsync(role))
        {
            await roleManager.CreateAsync(new ApplicationRole
            {
                Name = role,
                Description = GetRoleDescription(role)
            });
        }
    }

    // Create default admin user
    if (await userManager.FindByEmailAsync("<EMAIL>") == null)
    {
        var adminUser = new ApplicationUser
        {
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            FullName = "مدير النظام",
            EmailConfirmed = true,
            IsActive = true
        };

        var result = await userManager.CreateAsync(adminUser, "Admin@123");
        if (result.Succeeded)
        {
            await userManager.AddToRoleAsync(adminUser, "المدير_العام");
        }
    }

    // Create default offices
    if (!context.Offices.Any())
    {
        var offices = new List<Office>
        {
            new Office
            {
                Name = "الشؤون العامة",
                Description = "مكتب الشؤون العامة - صلاحية البحث والاستعلام فقط + إدارة المراسلات",
                Permissions = OfficePermissions.View,
                AllowedEmployeeTypes = System.Text.Json.JsonSerializer.Serialize(new[] { EmployeeType.Officer, EmployeeType.NCO, EmployeeType.Personnel, EmployeeType.Employee })
            },
            new Office
            {
                Name = "شؤون الضباط",
                Description = "مكتب شؤون الضباط - صلاحيات كاملة للضباط فقط",
                Permissions = OfficePermissions.All,
                AllowedEmployeeTypes = System.Text.Json.JsonSerializer.Serialize(new[] { EmployeeType.Officer })
            },
            new Office
            {
                Name = "شؤون ضباط الصف والأفراد",
                Description = "مكتب شؤون ضباط الصف والأفراد - صلاحيات كاملة لضباط الصف والأفراد فقط",
                Permissions = OfficePermissions.All,
                AllowedEmployeeTypes = System.Text.Json.JsonSerializer.Serialize(new[] { EmployeeType.NCO, EmployeeType.Personnel })
            },
            new Office
            {
                Name = "شؤون الموظفين",
                Description = "مكتب شؤون الموظفين المدنيين - صلاحيات كاملة للموظفين المدنيين فقط",
                Permissions = OfficePermissions.All,
                AllowedEmployeeTypes = System.Text.Json.JsonSerializer.Serialize(new[] { EmployeeType.Employee })
            }
        };

        context.Offices.AddRange(offices);
        await context.SaveChangesAsync();
    }
}

static string GetRoleDescription(string roleName)
{
    return roleName switch
    {
        "المدير_العام" => "المدير العام - صلاحيات كاملة على النظام",
        "المدير_الإداري" => "المدير الإداري - صلاحيات إدارية عامة",
        "شؤون_الضباط" => "موظف شؤون الضباط",
        "شؤون_ضباط_الصف" => "موظف شؤون ضباط الصف والأفراد",
        "شؤون_الموظفين" => "موظف شؤون الموظفين المدنيين",
        "الشؤون_العامة" => "موظف الشؤون العامة",
        "الكشوف_الشهرية" => "موظف الكشوف الشهرية",
        "المراسلات" => "موظف المراسلات",
        "مدير_قسم" => "مدير قسم",
        "مستخدم" => "مستخدم عادي",
        _ => "دور غير محدد"
    };
}
