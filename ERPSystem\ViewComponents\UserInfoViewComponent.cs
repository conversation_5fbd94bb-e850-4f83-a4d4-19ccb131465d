using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using ERPSystem.Models.Entities;

namespace ERPSystem.ViewComponents
{
    public class UserInfoViewComponent : ViewComponent
    {
        private readonly UserManager<ApplicationUser> _userManager;

        public UserInfoViewComponent(UserManager<ApplicationUser> userManager)
        {
            _userManager = userManager;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            if (User.Identity?.IsAuthenticated == true)
            {
                var user = await _userManager.GetUserAsync(HttpContext.User);
                var displayName = !string.IsNullOrEmpty(user?.FullName) ? user.FullName : User.Identity.Name;
                return Content(displayName ?? "");
            }
            return Content("");
        }
    }
}
