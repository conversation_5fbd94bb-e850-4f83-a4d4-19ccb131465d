﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة الموارد البشرية</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/ERPSystem.styles.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-light fixed-top">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <div class="brand-container">
                        <div class="brand-logo">
                            <img src="~/Logo.png" alt="شعار وزارة الداخلية" class="navbar-logo" />
                        </div>
                        <div class="brand-text">
                            <span class="brand-title">نظام إدارة الموارد البشرية</span>
                            <span class="brand-subtitle">وزارة الداخلية</span>
                        </div>
                    </div>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <ul class="navbar-nav flex-grow-1 justify-content-center">
                            <li class="nav-item">
                                <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                                    <i class="fas fa-home"></i>
                                    <span>الرئيسية</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-area="" asp-controller="Employee" asp-action="Index">
                                    <i class="fas fa-users"></i>
                                    <span>الموظفين</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-area="" asp-controller="Correspondence" asp-action="Index">
                                    <i class="fas fa-envelope"></i>
                                    <span>المراسلات</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-area="" asp-controller="Archive" asp-action="Index">
                                    <i class="fas fa-archive"></i>
                                    <span>الأرشيف</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-area="" asp-controller="Map" asp-action="Index">
                                    <i class="fas fa-map-marked-alt"></i>
                                    <span>الخريطة</span>
                                </a>
                            </li>
                        </ul>
                    }
                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle user-menu" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="user-avatar">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <span class="user-name">@await Component.InvokeAsync("UserInfo")</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li class="dropdown-header">
                                        <strong>@await Component.InvokeAsync("UserInfo")</strong>
                                        <small>مدير النظام</small>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#">
                                            <i class="fas fa-user"></i>
                                            الملف الشخصي
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#">
                                            <i class="fas fa-cog"></i>
                                            الإعدادات
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#">
                                            <i class="fas fa-bell"></i>
                                            الإشعارات
                                            <span class="badge bg-danger">3</span>
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline w-100">
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="fas fa-sign-out-alt"></i>
                                                تسجيل الخروج
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link login-link" asp-controller="Account" asp-action="Login">
                                    <i class="fas fa-sign-in-alt"></i>
                                    <span>تسجيل الدخول</span>
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="main-content">
        <div class="container-fluid">
            <main role="main" class="pb-5">
                @RenderBody()
            </main>
        </div>
    </div>

    <footer class="modern-footer">
        <div class="container-fluid">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo-section">
                        <img src="~/Logo.png" alt="شعار وزارة الداخلية" class="footer-logo" />
                        <div class="footer-logo-text">
                            <h6 class="footer-title">نظام إدارة الموارد البشرية</h6>
                            <p class="footer-text">وزارة الداخلية - دولة ليبيا</p>
                        </div>
                    </div>
                </div>
                <div class="footer-section">
                    <h6 class="footer-title">روابط سريعة</h6>
                    <ul class="footer-links">
                        <li><a href="#" class="footer-link">الدعم الفني</a></li>
                        <li><a href="#" class="footer-link">دليل المستخدم</a></li>
                        <li><a href="#" class="footer-link">الأسئلة الشائعة</a></li>
                        <li><a href="#" class="footer-link">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h6 class="footer-title">تواصل معنا</h6>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>+218 21 123 4567</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>طرابلس - دولة ليبيا</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright">
                    <p>&copy; 2025 وزارة الداخلية - دولة ليبيا. جميع الحقوق محفوظة.</p>
                </div>
                <div class="developer-info">
                    <div class="developer-section">
                        <span class="developer-label">تصميم و تطوير:</span>
                        <span class="developer-name">مهندس طارق مروان</span>
                    </div>
                </div>
                <div class="footer-version">
                    <span>الإصدار 1.0.0</span>
                </div>
            </div>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>



    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
