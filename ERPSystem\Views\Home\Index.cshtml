﻿@{
    ViewData["Title"] = "الصفحة الرئيسية";
}

<div class="container-fluid">
    @if (User.Identity?.IsAuthenticated == true)
    {
        <div class="row mb-5">
            <div class="col-12">
                <div class="hero-section">
                    <div class="hero-content">
                        <div class="hero-logo">
                            <img src="~/Logo.jpg" alt="شعار وزارة الداخلية" class="hero-logo-img" />
                        </div>
                        <h1 class="hero-title">
                            مرحباً بك في نظام إدارة الموارد البشرية
                        </h1>
                        <p class="hero-subtitle">وزارة الداخلية - دولة ليبيا</p>
                        <div class="hero-divider"></div>
                        <p class="hero-description">
                            نظام شامل ومتطور لإدارة الموارد البشرية والمراسلات والأرشيف الإلكتروني
                            <br>
                            مصمم خصيصاً لتلبية احتياجات وزارة الداخلية
                        </p>
                        <div class="hero-stats">
                            <div class="stat-item">
                                <div class="stat-number">0</div>
                                <div class="stat-label">إجمالي الموظفين</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">0</div>
                                <div class="stat-label">المراسلات النشطة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">4</div>
                                <div class="stat-label">المكاتب الإدارية</div>
                            </div>
                        </div>
                    </div>
                    <div class="hero-decoration">
                        <div class="decoration-circle circle-1"></div>
                        <div class="decoration-circle circle-2"></div>
                        <div class="decoration-circle circle-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="module-card employees-module">
                    <div class="module-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="module-content">
                        <h5 class="module-title">إدارة الموظفين</h5>
                        <p class="module-description">إدارة شاملة لبيانات الموظفين والضباط وضباط الصف مع نظام السجلات المتقدم</p>
                        <div class="module-stats">
                            <span class="stat-badge">0 موظف</span>
                        </div>
                    </div>
                    <a href="@Url.Action("Index", "Employee")" class="module-btn">
                        <span>دخول</span>
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div class="module-overlay"></div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="module-card correspondence-module">
                    <div class="module-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="module-content">
                        <h5 class="module-title">المراسلات</h5>
                        <p class="module-description">نظام متطور لإدارة المراسلات الواردة والصادرة مع تتبع سير العمل</p>
                        <div class="module-stats">
                            <span class="stat-badge">0 مراسلة</span>
                        </div>
                    </div>
                    <a href="@Url.Action("Index", "Correspondence")" class="module-btn">
                        <span>دخول</span>
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div class="module-overlay"></div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="module-card archive-module">
                    <div class="module-icon">
                        <i class="fas fa-archive"></i>
                    </div>
                    <div class="module-content">
                        <h5 class="module-title">الأرشيف الإلكتروني</h5>
                        <p class="module-description">نظام أرشفة إلكتروني متقدم مع التشفير وعرض المستندات</p>
                        <div class="module-stats">
                            <span class="stat-badge">0 وثيقة</span>
                        </div>
                    </div>
                    <a href="@Url.Action("Index", "Archive")" class="module-btn">
                        <span>دخول</span>
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div class="module-overlay"></div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="module-card map-module">
                    <div class="module-icon">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <div class="module-content">
                        <h5 class="module-title">الخريطة التفاعلية</h5>
                        <p class="module-description">خريطة ليبيا التفاعلية مع الإحصائيات والوحدات الإدارية</p>
                        <div class="module-stats">
                            <span class="stat-badge">5 مستويات</span>
                        </div>
                    </div>
                    <a href="@Url.Action("Index", "Map")" class="module-btn">
                        <span>دخول</span>
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div class="module-overlay"></div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-chart-bar"></i> إحصائيات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h3 class="text-primary">0</h3>
                                <p>إجمالي الموظفين</p>
                            </div>
                            <div class="col-6">
                                <h3 class="text-success">0</h3>
                                <p>المراسلات النشطة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-bell"></i> الإشعارات</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">لا توجد إشعارات جديدة</p>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="row justify-content-center">
            <div class="col-md-8 text-center">
                <div class="jumbotron bg-light p-5 rounded">
                    <h1 class="display-4"><i class="fas fa-building"></i> نظام إدارة الموارد البشرية</h1>
                    <p class="lead">وزارة الداخلية - دولة ليبيا</p>
                    <hr class="my-4">
                    <p>نظام شامل لإدارة الموارد البشرية والمراسلات والأرشيف الإلكتروني</p>
                    <a class="btn btn-primary btn-lg" href="@Url.Action("Login", "Account")" role="button">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                </div>
            </div>
        </div>
    }
</div>
