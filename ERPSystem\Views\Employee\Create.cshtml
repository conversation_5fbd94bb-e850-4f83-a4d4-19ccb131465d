@model ERPSystem.Models.ViewModels.EmployeeCreateViewModel
@{
    ViewData["Title"] = "إضافة منتسب جديد";
}

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="page-title">
                    <i class="fas fa-user-plus text-primary"></i>
                    إضافة منتسب جديد
                </h2>
                <a asp-action="Index" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <form asp-action="Create" method="post" class="needs-validation" novalidate>
        <div class="row">
            <!-- Basic Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user"></i>
                            البيانات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.FullName" class="form-label required"></label>
                                <input asp-for="Employee.FullName" class="form-control" required>
                                <span asp-validation-for="Employee.FullName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.NationalId" class="form-label required"></label>
                                <input asp-for="Employee.NationalId" class="form-control" required>
                                <span asp-validation-for="Employee.NationalId" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.EmployeeType" class="form-label required"></label>
                                <select asp-for="Employee.EmployeeType" class="form-select" required>
                                    <option value="">اختر نوع المنتسب</option>
                                    <option value="1">ضابط</option>
                                    <option value="2">ضابط صف</option>
                                    <option value="3">فرد</option>
                                    <option value="4">موظف مدني</option>
                                </select>
                                <span asp-validation-for="Employee.EmployeeType" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.EmploymentType" class="form-label"></label>
                                <select asp-for="Employee.EmploymentType" class="form-select">
                                    <option value="">اختر نوع التوظيف</option>
                                    <option value="1">تعيين</option>
                                    <option value="2">تعاقد</option>
                                </select>
                                <span asp-validation-for="Employee.EmploymentType" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.EmployeeNumber" class="form-label"></label>
                                <input asp-for="Employee.EmployeeNumber" class="form-control">
                                <span asp-validation-for="Employee.EmployeeNumber" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.MilitaryNumber" class="form-label"></label>
                                <input asp-for="Employee.MilitaryNumber" class="form-control">
                                <span asp-validation-for="Employee.MilitaryNumber" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Job Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-briefcase"></i>
                            بيانات العمل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.Rank" class="form-label"></label>
                                <input asp-for="Employee.Rank" class="form-control">
                                <span asp-validation-for="Employee.Rank" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.Position" class="form-label"></label>
                                <input asp-for="Employee.Position" class="form-control">
                                <span asp-validation-for="Employee.Position" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.Department" class="form-label"></label>
                                <input asp-for="Employee.Department" class="form-control">
                                <span asp-validation-for="Employee.Department" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.Unit" class="form-label"></label>
                                <input asp-for="Employee.Unit" class="form-control">
                                <span asp-validation-for="Employee.Unit" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.SubUnit" class="form-label"></label>
                                <input asp-for="Employee.SubUnit" class="form-control">
                                <span asp-validation-for="Employee.SubUnit" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.HireDate" class="form-label"></label>
                                <input asp-for="Employee.HireDate" type="date" class="form-control">
                                <span asp-validation-for="Employee.HireDate" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Personal Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-id-card"></i>
                            البيانات الشخصية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.DateOfBirth" class="form-label"></label>
                                <input asp-for="Employee.DateOfBirth" type="date" class="form-control">
                                <span asp-validation-for="Employee.DateOfBirth" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.PlaceOfBirth" class="form-label"></label>
                                <input asp-for="Employee.PlaceOfBirth" class="form-control">
                                <span asp-validation-for="Employee.PlaceOfBirth" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.Nationality" class="form-label"></label>
                                <input asp-for="Employee.Nationality" class="form-control" value="ليبي">
                                <span asp-validation-for="Employee.Nationality" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.Religion" class="form-label"></label>
                                <input asp-for="Employee.Religion" class="form-control" value="الإسلام">
                                <span asp-validation-for="Employee.Religion" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.MaritalStatus" class="form-label"></label>
                                <select asp-for="Employee.MaritalStatus" class="form-select">
                                    <option value="">اختر الحالة الاجتماعية</option>
                                    <option value="أعزب">أعزب</option>
                                    <option value="متزوج">متزوج</option>
                                    <option value="مطلق">مطلق</option>
                                    <option value="أرمل">أرمل</option>
                                </select>
                                <span asp-validation-for="Employee.MaritalStatus" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.NumberOfChildren" class="form-label"></label>
                                <input asp-for="Employee.NumberOfChildren" type="number" min="0" class="form-control">
                                <span asp-validation-for="Employee.NumberOfChildren" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-phone"></i>
                            بيانات الاتصال
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.Phone" class="form-label"></label>
                                <input asp-for="Employee.Phone" type="tel" class="form-control">
                                <span asp-validation-for="Employee.Phone" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Employee.Email" class="form-label"></label>
                                <input asp-for="Employee.Email" type="email" class="form-control">
                                <span asp-validation-for="Employee.Email" class="text-danger"></span>
                            </div>
                            <div class="col-12 mb-3">
                                <label asp-for="Employee.Address" class="form-label"></label>
                                <textarea asp-for="Employee.Address" class="form-control" rows="3"></textarea>
                                <span asp-validation-for="Employee.Address" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Side Panel -->
            <div class="col-lg-4">
                <!-- Office and Location -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-building"></i>
                            المكتب والموقع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label asp-for="Employee.OfficeId" class="form-label"></label>
                            <select asp-for="Employee.OfficeId" class="form-select">
                                <option value="">اختر المكتب</option>
                                @foreach (var office in Model.Offices)
                                {
                                    <option value="@office.Id">@office.Name</option>
                                }
                            </select>
                            <span asp-validation-for="Employee.OfficeId" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="Employee.MapLocationId" class="form-label"></label>
                            <select asp-for="Employee.MapLocationId" class="form-select">
                                <option value="">اختر الموقع</option>
                                @foreach (var location in Model.MapLocations)
                                {
                                    <option value="@location.Id">@location.ArabicName</option>
                                }
                            </select>
                            <span asp-validation-for="Employee.MapLocationId" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <!-- Qualifications -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-graduation-cap"></i>
                            المؤهلات العلمية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label asp-for="Employee.Qualification" class="form-label"></label>
                            <input asp-for="Employee.Qualification" class="form-control">
                            <span asp-validation-for="Employee.Qualification" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="Employee.QualificationAuthority" class="form-label"></label>
                            <input asp-for="Employee.QualificationAuthority" class="form-control">
                            <span asp-validation-for="Employee.QualificationAuthority" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="Employee.QualityAccreditation" class="form-label"></label>
                            <input asp-for="Employee.QualityAccreditation" class="form-control">
                            <span asp-validation-for="Employee.QualityAccreditation" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i>
                                حفظ البيانات
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // Auto-generate employee number based on type
        document.querySelector('select[name="Employee.EmployeeType"]').addEventListener('change', function() {
            const employeeNumberField = document.querySelector('input[name="Employee.EmployeeNumber"]');
            if (!employeeNumberField.value) {
                const type = this.value;
                const year = new Date().getFullYear();
                const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
                
                let prefix = '';
                switch(type) {
                    case '1': prefix = 'OFF'; break; // Officer
                    case '2': prefix = 'NCO'; break; // NCO
                    case '3': prefix = 'PER'; break; // Personnel
                    case '4': prefix = 'EMP'; break; // Employee
                }
                
                if (prefix) {
                    employeeNumberField.value = `${prefix}${year}${random}`;
                }
            }
        });
    </script>
}

@section Styles {
    <style>
        .required::after {
            content: " *";
            color: #dc3545;
        }

        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
            border-radius: 12px 12px 0 0 !important;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #d1d5db;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #1e40af;
            box-shadow: 0 0 0 0.2rem rgba(30, 64, 175, 0.25);
        }

        .btn {
            border-radius: 8px;
            font-weight: 600;
        }

        .page-title {
            color: #1e40af;
            font-weight: 700;
        }
    </style>
}
