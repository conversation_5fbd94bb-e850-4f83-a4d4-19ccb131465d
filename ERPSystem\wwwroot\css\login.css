/* Login Page Specific Styles */
.login-container {
    position: relative;
    min-height: 100vh;
    overflow: hidden;
}

.login-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="transparent"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="700" cy="800" r="80" fill="url(%23a)"/></svg>');
    background-size: cover, 100% 100%;
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.05) rotate(1deg); }
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    position: relative;
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    padding: 3rem 2rem 2rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    position: relative;
}

.login-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="70" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.3;
}

.login-logo {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    position: relative;
    z-index: 1;
    animation: pulse 2s ease-in-out infinite;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.login-logo-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
    padding: 2px;
    mix-blend-mode: multiply;
    filter: contrast(1.2) brightness(1.1);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.login-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.login-subtitle {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.login-subtitle-small {
    font-size: 0.9rem;
    opacity: 0.8;
    position: relative;
    z-index: 1;
}

.login-body {
    padding: 2.5rem;
}

.form-floating {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-floating .form-control:focus ~ label,
.form-floating .form-control:not(:placeholder-shown) ~ label {
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    padding: 0.25rem 0.5rem 0.25rem 2.5rem;
}

.form-floating .form-control:focus ~ label i,
.form-floating .form-control:not(:placeholder-shown) ~ label i {
    right: 0.5rem;
    font-size: 0.9rem;
}

.login-input {
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 15px;
    padding: 1.2rem 3rem 0.6rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    direction: rtl;
    text-align: right;
}

.login-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

.form-floating > label {
    padding: 1rem 1rem 1rem 3rem;
    color: #6c757d;
    font-weight: 500;
    direction: rtl;
    text-align: right;
    right: 0;
    left: auto;
}

.form-floating > label i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #667eea;
    font-size: 1.1rem;
}

.custom-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.custom-checkbox .form-check-input {
    width: 20px;
    height: 20px;
    border: 2px solid #667eea;
    border-radius: 6px;
    background: transparent;
}

.custom-checkbox .form-check-input:checked {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-color: #667eea;
}

.custom-checkbox .form-check-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: #495057;
}

.login-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    border-radius: 15px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.login-btn:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.login-btn:hover::before {
    left: 100%;
}

.login-footer {
    padding: 2.5rem 2rem;
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.9), rgba(255, 255, 255, 0.8));
    border-top: 1px solid rgba(102, 126, 234, 0.1);
    backdrop-filter: blur(10px);
}

.login-features {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.feature-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 20px;
    transition: all 0.3s ease;
    min-width: 120px;
    animation: featureBadgeSlide 0.8s ease-out calc(0.2s * var(--index, 0)) both;
}

.feature-badge:nth-child(1) { --index: 1; }
.feature-badge:nth-child(2) { --index: 2; }
.feature-badge:nth-child(3) { --index: 3; }

.feature-badge:hover {
    transform: translateY(-5px) scale(1.05);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
}

.feature-badge i {
    font-size: 1.5rem;
    color: #667eea;
    transition: all 0.3s ease;
}

.feature-badge:hover i {
    transform: scale(1.2);
    color: #5a6fd8;
}

.feature-badge span {
    font-size: 0.85rem;
    font-weight: 600;
    color: #5a6c7d;
    text-align: center;
    transition: color 0.3s ease;
}

.feature-badge:hover span {
    color: #495057;
}

.copyright {
    text-align: center;
    color: #6c757d;
    font-size: 0.9rem;
    padding: 1.5rem 0 0;
    border-top: 1px solid rgba(102, 126, 234, 0.1);
    animation: copyrightFade 1s ease-out 1.5s both;
}

.copyright p {
    margin: 0.25rem 0;
    transition: color 0.3s ease;
}

.copyright p:first-child {
    font-weight: 600;
    color: #5a6c7d;
}

.copyright .version {
    font-size: 0.8rem;
    color: #8e9aaf;
    font-style: italic;
}

.validation-message {
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: block;
}

.login-alert {
    border-radius: 10px;
    border: none;
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border-left: 4px solid #dc3545;
}

/* Animations */
@keyframes featureBadgeSlide {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes copyrightFade {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-header {
        padding: 2rem 1.5rem 1.5rem;
    }

    .login-body {
        padding: 2rem 1.5rem;
    }

    .login-footer {
        padding: 1.5rem;
    }

    .login-title {
        font-size: 1.75rem;
    }

    .login-logo {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .login-input {
        padding: 1rem 2.5rem 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .form-floating > label {
        padding: 0.8rem 0.8rem 0.8rem 2.5rem;
        font-size: 0.9rem;
    }

    .form-floating > label i {
        right: 0.8rem;
        font-size: 1rem;
    }

    .login-features {
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .feature-badge {
        padding: 0.8rem 1rem;
        min-width: 100px;
    }

    .feature-badge i {
        font-size: 1.3rem;
    }

    .feature-badge span {
        font-size: 0.8rem;
    }

    .copyright {
        padding: 1rem 0 0;
    }
}
