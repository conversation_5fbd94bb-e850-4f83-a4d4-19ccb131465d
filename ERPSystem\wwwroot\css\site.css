/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap');

/* Arabic RTL Support */
html {
  font-size: 14px;
  direction: rtl;
  text-align: right;
  scroll-behavior: smooth;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: 'Cairo', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  direction: rtl;
  text-align: right;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  min-height: 100vh;
}

/* Enhanced Navbar */
.navbar {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 1rem 0;
  z-index: 1050;
}

.brand-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  text-decoration: none;
}

.brand-logo {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.navbar-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 12px;
  padding: 2px;
  mix-blend-mode: multiply;
  filter: contrast(1.2) brightness(1.1);
}

.brand-container:hover .brand-logo {
  transform: scale(1.05) rotate(2deg);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.brand-text {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.brand-title {
  font-weight: 700;
  font-size: 1.4rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.brand-subtitle {
  font-size: 0.9rem;
  color: #6c757d;
  font-weight: 500;
}

.navbar-brand:hover {
  text-decoration: none;
}

.navbar-nav .nav-link {
  padding: 0.8rem 1.5rem;
  font-weight: 600;
  color: #2c3e50 !important;
  border-radius: 25px;
  margin: 0 0.3rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.navbar-nav .nav-link i {
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  text-decoration: none;
}

.navbar-nav .nav-link:hover i {
  transform: scale(1.1);
}

.navbar-nav .nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.navbar-nav .nav-link:hover::before {
  left: 100%;
}

/* User Menu Styles */
.user-menu {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 25px;
  transition: all 0.3s ease;
}

.user-menu:hover {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white !important;
  border-color: transparent;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.user-avatar {
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
}

.user-name {
  font-weight: 600;
  font-size: 0.95rem;
}

.login-link {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white !important;
  border: none;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-link:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.dropdown-menu {
  right: -10px;
  left: auto;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 15px;
  box-shadow:
    0 15px 40px rgba(0, 0, 0, 0.12),
    0 6px 20px rgba(102, 126, 234, 0.08);
  padding: 0;
  min-width: 240px;
  max-width: 260px;
  margin-top: 0.5rem;
  direction: rtl;
  text-align: right;
  animation: dropdownSlideIn 0.3s ease-out;
  transform: translateX(0);
}

/* Ensure dropdown stays within viewport */
.dropdown-menu.show {
  position: absolute !important;
}

@media (max-width: 576px) {
  .dropdown-menu {
    right: -30px;
    min-width: 200px;
    max-width: 220px;
  }
}

@media (max-width: 480px) {
  .dropdown-menu {
    right: -40px;
    min-width: 180px;
    max-width: 200px;
  }
}

.dropdown-header {
  padding: 0 1.5rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.user-dropdown-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 1rem;
  border-radius: 15px 15px 0 0;
  border-bottom: none;
  margin-bottom: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  direction: rtl;
}

.user-avatar-large {
  width: 45px;
  height: 45px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  text-align: right;
}

.user-name-large {
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 0.2rem;
}

.user-role {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-bottom: 0.3rem;
}

.user-status {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.7rem;
  opacity: 0.8;
  justify-content: flex-end;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4ade80;
  animation: statusPulse 2s ease-in-out infinite;
}

.elegant-divider {
  margin: 0.5rem 1rem;
  border-color: rgba(102, 126, 234, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar-large {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.user-details {
  flex: 1;
}

.user-name-large {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.user-role {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
}

.dropdown-item {
  padding: 0.8rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border: none;
  background: none;
  width: 100%;
  text-align: right;
}

.user-dropdown-item {
  padding: 0.7rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  transition: all 0.3s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: right;
  position: relative;
  direction: rtl;
}

.user-dropdown-item:hover {
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  transform: translateX(-5px);
  color: inherit;
  text-decoration: none;
}

.item-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.item-icon i {
  font-size: 1.1rem;
  color: #667eea;
  transition: all 0.3s ease;
}

.user-dropdown-item:hover .item-icon {
  background: linear-gradient(45deg, #667eea, #764ba2);
  transform: scale(1.1);
}

.user-dropdown-item:hover .item-icon i {
  color: white;
}

.item-content {
  flex: 1;
  text-align: right;
}

.item-title {
  display: block;
  font-weight: 600;
  font-size: 0.85rem;
  color: #2c3e50;
  margin-bottom: 0.1rem;
}

.item-desc {
  display: block;
  font-size: 0.7rem;
  color: #6c757d;
  font-weight: 400;
}

.notification-badge {
  position: absolute;
  top: 0.8rem;
  left: 1.5rem;
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  animation: badgePulse 2s ease-in-out infinite;
}

.dropdown-item i {
  width: 18px;
  text-align: center;
  color: #667eea;
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  transform: translateX(-5px);
}

.dropdown-item:hover i {
  color: white;
  transform: scale(1.1);
}

.logout-btn {
  color: #dc3545 !important;
}

.logout-btn:hover {
  background: linear-gradient(45deg, #dc3545, #c82333) !important;
  color: white !important;
}

.logout-icon {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(200, 35, 51, 0.1)) !important;
}

.logout-btn:hover .logout-icon {
  background: rgba(255, 255, 255, 0.2) !important;
}

.logout-btn:hover .logout-icon i {
  color: white !important;
}

/* Animations */
@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes badgePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.logout-btn i {
  color: #dc3545;
}

.logout-btn:hover i {
  color: white;
}

/* Enhanced Jumbotron */
.jumbotron {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%, #f093fb 200%);
  border-radius: 25px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.jumbotron::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.jumbotron * {
  position: relative;
  z-index: 1;
}

/* Enhanced Cards */
.card {
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.card:hover::before {
  transform: scaleX(1);
}

.card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.2);
}

.card-body {
  padding: 2rem;
  position: relative;
}

.card-body::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.card:hover .card-body::after {
  width: 300px;
  height: 300px;
}

/* Enhanced Buttons */
.btn {
  border-radius: 25px;
  font-weight: 600;
  padding: 0.8rem 2rem;
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.btn-success {
  background: linear-gradient(45deg, #56ab2f, #a8e6cf);
  box-shadow: 0 8px 25px rgba(86, 171, 47, 0.3);
}

.btn-success:hover {
  background: linear-gradient(45deg, #4a9025, #96d4b5);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(86, 171, 47, 0.4);
}

.btn-warning {
  background: linear-gradient(45deg, #f7971e, #ffd200);
  box-shadow: 0 8px 25px rgba(247, 151, 30, 0.3);
}

.btn-warning:hover {
  background: linear-gradient(45deg, #e5851a, #e6bd00);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(247, 151, 30, 0.4);
}

.btn-info {
  background: linear-gradient(45deg, #00d2ff, #3a7bd5);
  box-shadow: 0 8px 25px rgba(0, 210, 255, 0.3);
}

.btn-info:hover {
  background: linear-gradient(45deg, #00bde5, #3269c0);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(0, 210, 255, 0.4);
}

.btn-lg {
  padding: 1.2rem 3rem;
  font-size: 1.1rem;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

/* Arabic Text Improvements */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

/* Form Improvements */
.form-control {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Card Enhancements */
.card-header {
  font-weight: 600;
  border-bottom: 2px solid rgba(0,0,0,0.1);
}

/* Icon Styling */
.fa-3x {
  margin-bottom: 1rem;
}

/* Enhanced Hero Section */
.hero-section {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(20px);
  border-radius: 30px;
  padding: 4rem 3rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
  margin-bottom: 3rem;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-logo {
  width: 160px;
  height: 160px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2.5rem;
  box-shadow:
    0 20px 60px rgba(102, 126, 234, 0.4),
    0 8px 25px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: heroLogoFloat 4s ease-in-out infinite;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.4);
  position: relative;
}

.hero-logo::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: logoShine 3s ease-in-out infinite;
  pointer-events: none;
}

.hero-logo:hover {
  transform: scale(1.05);
  box-shadow:
    0 25px 80px rgba(102, 126, 234, 0.5),
    0 12px 35px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.6);
}

.hero-logo-img {
  width: 85%;
  height: 85%;
  object-fit: contain;
  border-radius: 50%;
  transition: all 0.4s ease;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.2));
  position: relative;
  z-index: 2;
}

.hero-logo:hover .hero-logo-img {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 6px 20px rgba(0, 0, 0, 0.3));
}

@keyframes heroLogoFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    box-shadow:
      0 20px 60px rgba(102, 126, 234, 0.4),
      0 8px 25px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  25% {
    transform: translateY(-8px) rotate(1deg);
  }
  50% {
    transform: translateY(-15px) rotate(0deg);
    box-shadow:
      0 25px 70px rgba(102, 126, 234, 0.5),
      0 12px 30px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
  75% {
    transform: translateY(-8px) rotate(-1deg);
  }
}

@keyframes logoShine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Welcome Card Styles */
.welcome-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 30px;
  box-shadow:
    0 30px 80px rgba(102, 126, 234, 0.3),
    0 15px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  margin: 2rem 0;
  animation: welcomeSlideUp 1s ease-out;
}

.welcome-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="transparent"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="700" cy="800" r="80" fill="url(%23a)"/></svg>');
  background-size: cover, 100% 100%;
  opacity: 0.1;
  z-index: 1;
  animation: backgroundPulse 8s ease-in-out infinite;
}

.welcome-content {
  position: relative;
  z-index: 2;
  padding: 4rem 3rem;
  text-align: center;
}

.welcome-logo {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  backdrop-filter: blur(15px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  box-shadow:
    0 15px 40px rgba(102, 126, 234, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  animation: welcomeLogoFloat 3s ease-in-out infinite;
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.welcome-logo-img {
  width: 70%;
  height: 70%;
  object-fit: contain;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.2));
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: welcomeTextSlide 1s ease-out 0.3s both;
}

.welcome-subtitle {
  font-size: 1.3rem;
  font-weight: 500;
  color: #667eea;
  margin-bottom: 2rem;
  animation: welcomeTextSlide 1s ease-out 0.5s both;
}

.welcome-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 2px;
  margin: 0 auto 2rem;
  animation: welcomeDividerExpand 1s ease-out 0.7s both;
}

.welcome-description {
  font-size: 1.1rem;
  color: #5a6c7d;
  line-height: 1.8;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  animation: welcomeTextSlide 1s ease-out 0.9s both;
}

.welcome-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 15px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
  animation: welcomeFeatureSlide 1s ease-out calc(1.1s + var(--delay, 0s)) both;
}

.feature-item:nth-child(1) { --delay: 0s; }
.feature-item:nth-child(2) { --delay: 0.1s; }
.feature-item:nth-child(3) { --delay: 0.2s; }

.feature-item:hover {
  transform: translateY(-5px);
  background: rgba(102, 126, 234, 0.15);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
}

.feature-item i {
  font-size: 1.5rem;
  color: #667eea;
}

.feature-item span {
  font-size: 0.9rem;
  font-weight: 500;
  color: #5a6c7d;
}

.welcome-btn {
  display: inline-block;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 1.2rem 3rem;
  border-radius: 50px;
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
  animation: welcomeBtnSlide 1s ease-out 1.3s both;
}

.welcome-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 20px 50px rgba(102, 126, 234, 0.5);
  text-decoration: none;
  color: white;
}

.btn-text {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.welcome-btn:hover .btn-shine {
  left: 100%;
}

/* Animations */
@keyframes welcomeSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes backgroundPulse {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.15; }
}

@keyframes welcomeLogoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes welcomeTextSlide {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes welcomeDividerExpand {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 80px;
    opacity: 1;
  }
}

@keyframes welcomeFeatureSlide {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes welcomeBtnSlide {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.hero-title {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 1rem;
}

.hero-divider {
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
  margin: 2rem auto;
  animation: dividerGlow 2s ease-in-out infinite;
}

@keyframes dividerGlow {
  0%, 100% { box-shadow: 0 0 10px rgba(102, 126, 234, 0.3); }
  50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.6); }
}

.hero-description {
  font-size: 1.2rem;
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: block;
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  color: #6c757d;
  font-weight: 600;
  margin-top: 0.5rem;
}

.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  animation: decorationFloat 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: -50px;
  right: -50px;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: -30px;
  left: -30px;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  top: 50%;
  left: -20px;
  animation-delay: 4s;
}

@keyframes decorationFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-20px) scale(1.1); }
}

/* Enhanced Module Cards */
.module-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 2.5rem;
  height: 100%;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.module-card:hover::before {
  transform: scaleX(1);
}

.module-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 30px 70px rgba(0, 0, 0, 0.2);
}

.module-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  position: relative;
  transition: all 0.3s ease;
}

.employees-module .module-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.correspondence-module .module-icon {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
  color: white;
}

.archive-module .module-icon {
  background: linear-gradient(135deg, #f7971e, #ffd200);
  color: white;
}

.map-module .module-icon {
  background: linear-gradient(135deg, #00d2ff, #3a7bd5);
  color: white;
}

.module-card:hover .module-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.module-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.module-description {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.module-stats {
  margin-bottom: 2rem;
}

.stat-badge {
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  color: #667eea;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.module-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 15px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.module-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s;
}

.module-btn:hover::before {
  left: 100%;
}

.module-btn:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
  transform: translateX(-5px);
  color: white;
  text-decoration: none;
}

.module-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.module-card:hover .module-overlay {
  opacity: 1;
}

/* Animated Background Elements */
.container-fluid {
  position: relative;
}

.container-fluid::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  z-index: -1;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(10px) rotate(-1deg); }
}

/* Enhanced Statistics Cards */
.stats-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: rotate 4s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-card:hover::before {
  opacity: 1;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stats-number {
  font-size: 2.5rem;
  font-weight: 900;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 1;
}

/* Loading Animation */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Main Content Layout */
.main-content {
  padding-top: 120px;
  min-height: calc(100vh - 200px);
}

/* Enhanced Footer */
.modern-footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 3rem 0 1rem;
  margin-top: 4rem;
  position: relative;
  overflow: hidden;
}

.modern-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="70" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="30" cy="80" r="1.5" fill="rgba(255,255,255,0.05)"/><circle cx="70" cy="60" r="1" fill="rgba(255,255,255,0.05)"/></svg>');
  opacity: 0.3;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.footer-section {
  padding: 1rem;
}

.footer-logo-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.footer-logo {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  object-fit: contain;
  background: transparent;
  border: 3px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  padding: 2px;
  mix-blend-mode: multiply;
  filter: contrast(1.2) brightness(1.1);
}

.footer-logo:hover {
  transform: scale(1.05);
  border-color: #667eea;
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.3);
}

.footer-logo-text {
  flex: 1;
}

.footer-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #ecf0f1;
  position: relative;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  right: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
}

.footer-text {
  color: #bdc3c7;
  line-height: 1.6;
  margin-bottom: 0;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-link {
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-link::before {
  content: '◀';
  font-size: 0.7rem;
  color: #667eea;
  transition: all 0.3s ease;
}

.footer-link:hover {
  color: #667eea;
  text-decoration: none;
  transform: translateX(-5px);
}

.footer-link:hover::before {
  transform: translateX(-3px);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #bdc3c7;
}

.contact-item i {
  width: 20px;
  text-align: center;
  color: #667eea;
  font-size: 1.1rem;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.copyright p {
  margin: 0;
  color: #95a5a6;
  font-size: 0.9rem;
}

.developer-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.developer-section {
  background: rgba(102, 126, 234, 0.15);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  border: 1px solid rgba(102, 126, 234, 0.3);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
}

.developer-section:hover {
  background: rgba(102, 126, 234, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.developer-label {
  color: #bdc3c7;
  font-size: 0.85rem;
  font-weight: 500;
}

.developer-name {
  color: #667eea;
  font-size: 0.9rem;
  font-weight: 700;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-version {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  border: 1px solid rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.footer-version:hover {
  background: rgba(102, 126, 234, 0.3);
  transform: translateY(-2px);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .main-content {
    padding-top: 100px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.3rem;
  }

  .hero-stats {
    gap: 2rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .brand-title {
    font-size: 1.2rem;
  }

  .brand-subtitle {
    font-size: 0.8rem;
  }

  .navbar-nav .nav-link {
    padding: 0.6rem 1rem;
    margin: 0.2rem 0;
  }

  .module-card {
    padding: 2rem;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .footer-logo-section {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .footer-logo {
    width: 50px;
    height: 50px;
  }

  .developer-info {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .main-content {
    padding-top: 90px;
  }

  .hero-section {
    padding: 2.5rem 1.5rem;
    margin-bottom: 2rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-logo {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
  }

  .hero-logo::before {
    display: none;
  }

  /* Welcome Card Responsive */
  .welcome-content {
    padding: 3rem 2rem;
  }

  .welcome-logo {
    width: 80px;
    height: 80px;
  }

  .welcome-title {
    font-size: 2rem;
  }

  .welcome-subtitle {
    font-size: 1.1rem;
  }

  .welcome-description {
    font-size: 1rem;
  }

  .welcome-features {
    gap: 1rem;
  }

  .feature-item {
    padding: 0.8rem;
    min-width: 100px;
  }

  .welcome-btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }

  .module-card {
    padding: 1.5rem;
  }

  .brand-container {
    gap: 0.5rem;
  }

  .brand-logo {
    width: 35px;
    height: 35px;
  }

  .brand-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .dropdown-menu {
    min-width: 220px;
    max-width: 240px;
    right: -20px;
    transform: translateX(0);
  }

  .user-avatar-large {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .user-name-large {
    font-size: 0.85rem;
  }

  .user-dropdown-item {
    padding: 0.6rem 0.8rem;
  }

  .item-icon {
    width: 28px;
    height: 28px;
  }

  .item-title {
    font-size: 0.8rem;
  }

  .item-desc {
    font-size: 0.65rem;
  }
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}