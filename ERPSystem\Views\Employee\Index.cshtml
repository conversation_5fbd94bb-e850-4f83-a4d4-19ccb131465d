@model ERPSystem.Models.ViewModels.EmployeeIndexViewModel
@{
    ViewData["Title"] = "إدارة المنتسبين";
}

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="page-title">
                    <i class="fas fa-users text-primary"></i>
                    إدارة المنتسبين
                </h2>
                <div class="page-actions">
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة منتسب جديد
                    </a>
                    <a asp-action="Statistics" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i>
                        الإحصائيات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-search"></i>
                        البحث والفلترة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get" asp-action="Index">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label class="form-label">البحث</label>
                                <input type="text" name="search" value="@Model.CurrentSearch" 
                                       class="form-control" placeholder="الاسم، الرقم الوطني، رقم الموظف...">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">نوع المنتسب</label>
                                <select name="employeeType" class="form-select">
                                    <option value="">جميع الأنواع</option>
                                    <option value="1" selected="@(Model.CurrentEmployeeType == ERPSystem.Models.Entities.EmployeeType.Officer)">ضابط</option>
                                    <option value="2" selected="@(Model.CurrentEmployeeType == ERPSystem.Models.Entities.EmployeeType.NCO)">ضابط صف</option>
                                    <option value="3" selected="@(Model.CurrentEmployeeType == ERPSystem.Models.Entities.EmployeeType.Personnel)">فرد</option>
                                    <option value="4" selected="@(Model.CurrentEmployeeType == ERPSystem.Models.Entities.EmployeeType.Employee)">موظف مدني</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">المكتب</label>
                                <select name="office" class="form-select">
                                    <option value="">جميع المكاتب</option>
                                    @foreach (var office in Model.Offices)
                                    {
                                        <option value="@office.Id" selected="@(Model.CurrentOffice == office.Id)">@office.Name</option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                        بحث
                                    </button>
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        قائمة المنتسبين (@Model.TotalCount منتسب)
                    </h5>
                    <div class="card-actions">
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i>
                                تصدير
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-file-pdf"></i> PDF</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-file-excel"></i> Excel</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-file-csv"></i> CSV</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if (Model.Employees.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>الاسم الكامل</th>
                                        <th>الرقم الوطني</th>
                                        <th>نوع المنتسب</th>
                                        <th>الرتبة</th>
                                        <th>المنصب</th>
                                        <th>المكتب</th>
                                        <th>الهاتف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var employee in Model.Employees)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="employee-avatar me-3">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <strong>@employee.FullName</strong>
                                                        @if (!string.IsNullOrEmpty(employee.EmployeeNumber))
                                                        {
                                                            <br><small class="text-muted">رقم الموظف: @employee.EmployeeNumber</small>
                                                        }
                                                    </div>
                                                </div>
                                            </td>
                                            <td>@employee.NationalId</td>
                                            <td>
                                                @switch (employee.EmployeeType)
                                                {
                                                    case ERPSystem.Models.Entities.EmployeeType.Officer:
                                                        <span class="badge bg-primary">ضابط</span>
                                                        break;
                                                    case ERPSystem.Models.Entities.EmployeeType.NCO:
                                                        <span class="badge bg-success">ضابط صف</span>
                                                        break;
                                                    case ERPSystem.Models.Entities.EmployeeType.Personnel:
                                                        <span class="badge bg-info">فرد</span>
                                                        break;
                                                    case ERPSystem.Models.Entities.EmployeeType.Employee:
                                                        <span class="badge bg-warning">موظف مدني</span>
                                                        break;
                                                }
                                            </td>
                                            <td>@(employee.Rank ?? "-")</td>
                                            <td>@(employee.Position ?? "-")</td>
                                            <td>@(employee.Office?.Name ?? "-")</td>
                                            <td>@(employee.Phone ?? "-")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@employee.Id" 
                                                       class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@employee.Id" 
                                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete(@employee.Id, '@employee.FullName')" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (Model.TotalPages > 1)
                        {
                            <div class="card-footer">
                                <nav aria-label="صفحات النتائج">
                                    <ul class="pagination justify-content-center mb-0">
                                        @if (Model.HasPreviousPage)
                                        {
                                            <li class="page-item">
                                                <a class="page-link" asp-action="Index" 
                                                   asp-route-page="@(Model.CurrentPage - 1)"
                                                   asp-route-search="@Model.CurrentSearch"
                                                   asp-route-employeeType="@Model.CurrentEmployeeType"
                                                   asp-route-office="@Model.CurrentOffice">
                                                    السابق
                                                </a>
                                            </li>
                                        }

                                        @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                        {
                                            <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                                <a class="page-link" asp-action="Index" 
                                                   asp-route-page="@i"
                                                   asp-route-search="@Model.CurrentSearch"
                                                   asp-route-employeeType="@Model.CurrentEmployeeType"
                                                   asp-route-office="@Model.CurrentOffice">
                                                    @i
                                                </a>
                                            </li>
                                        }

                                        @if (Model.HasNextPage)
                                        {
                                            <li class="page-item">
                                                <a class="page-link" asp-action="Index" 
                                                   asp-route-page="@(Model.CurrentPage + 1)"
                                                   asp-route-search="@Model.CurrentSearch"
                                                   asp-route-employeeType="@Model.CurrentEmployeeType"
                                                   asp-route-office="@Model.CurrentOffice">
                                                    التالي
                                                </a>
                                            </li>
                                        }
                                    </ul>
                                </nav>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد نتائج</h5>
                            <p class="text-muted">لم يتم العثور على منتسبين بالمعايير المحددة</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                إضافة منتسب جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المنتسب <strong id="employeeName"></strong>؟</p>
                <p class="text-muted">سيتم إلغاء تفعيل المنتسب وليس حذفه نهائياً.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(employeeId, employeeName) {
            document.getElementById('employeeName').textContent = employeeName;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + employeeId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Auto-submit search form on dropdown change
        document.querySelectorAll('select[name="employeeType"], select[name="office"]').forEach(select => {
            select.addEventListener('change', function() {
                this.form.submit();
            });
        });
    </script>
}

@section Styles {
    <style>
        .employee-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .page-title {
            color: #1e40af;
            font-weight: 700;
        }

        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
            border-radius: 12px 12px 0 0 !important;
        }

        .table th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
            color: #1e293b;
        }

        .btn-group .btn {
            border-radius: 6px;
            margin: 0 2px;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.4rem 0.6rem;
        }
    </style>
}
