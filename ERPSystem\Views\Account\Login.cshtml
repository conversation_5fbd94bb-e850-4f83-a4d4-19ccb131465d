@model ERPSystem.Models.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
}

<div class="login-container">
    <div class="login-background"></div>
    <div class="row justify-content-center align-items-center min-vh-100">
        <div class="col-md-6 col-lg-5 col-xl-4">
            <div class="login-card">
                <div class="login-header">
                    <div class="login-logo">
                        <img src="~/Logo.jpg" alt="شعار وزارة الداخلية" class="login-logo-img" />
                    </div>
                    <h2 class="login-title">تسجيل الدخول</h2>
                    <p class="login-subtitle">نظام إدارة الموارد البشرية</p>
                    <p class="login-subtitle-small">وزارة الداخلية - دولة ليبيا</p>
                </div>
                <div class="login-body">
                    <form asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]" method="post" class="login-form">
                        <div asp-validation-summary="All" class="alert alert-danger login-alert"></div>

                        <div class="form-floating mb-4">
                            <input asp-for="Email" type="email" class="form-control login-input" placeholder="أدخل البريد الإلكتروني" required />
                            <label asp-for="Email" class="form-label">
                                <i class="fas fa-envelope"></i>البريد الإلكتروني
                            </label>
                            <span asp-validation-for="Email" class="text-danger validation-message"></span>
                        </div>

                        <div class="form-floating mb-4">
                            <input asp-for="Password" type="password" class="form-control login-input" placeholder="أدخل كلمة المرور" required />
                            <label asp-for="Password" class="form-label">
                                <i class="fas fa-lock"></i>كلمة المرور
                            </label>
                            <span asp-validation-for="Password" class="text-danger validation-message"></span>
                        </div>

                        <div class="form-check mb-4 custom-checkbox">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">
                                <i class="fas fa-check"></i>
                                <span>تذكرني</span>
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg w-100 login-btn">
                            <span class="btn-text">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                دخول
                            </span>
                            <div class="btn-loading d-none">
                                <div class="loading-spinner"></div>
                                جاري التحقق...
                            </div>
                        </button>
                    </form>
                </div>
                <div class="login-footer">
                    <div class="login-info">
                        <div class="info-card">
                            <div class="info-header">
                                <i class="fas fa-info-circle"></i>
                                <span>بيانات تسجيل الدخول الافتراضية</span>
                            </div>
                            <div class="info-content">
                                <div class="credential-item">
                                    <i class="fas fa-envelope"></i>
                                    <span><EMAIL></span>
                                </div>
                                <div class="credential-item">
                                    <i class="fas fa-key"></i>
                                    <span>Admin@123</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="copyright">
                        <p>© 2025 وزارة الداخلية - دولة ليبيا</p>
                        <p>نظام إدارة الموارد البشرية</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="~/css/login.css" />
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
