using ERPSystem.Models.Entities;
using System.ComponentModel.DataAnnotations;

namespace ERPSystem.Models.ViewModels
{
    public class EmployeeIndexViewModel
    {
        public IEnumerable<Employee> Employees { get; set; } = new List<Employee>();
        public IEnumerable<Office> Offices { get; set; } = new List<Office>();
        
        public string? CurrentSearch { get; set; }
        public EmployeeType? CurrentEmployeeType { get; set; }
        public string? CurrentOffice { get; set; }
        
        public int CurrentPage { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
    }

    public class EmployeeCreateViewModel
    {
        public Employee Employee { get; set; } = new Employee();
        public IEnumerable<Office> Offices { get; set; } = new List<Office>();
        public IEnumerable<MapLocation> MapLocations { get; set; } = new List<MapLocation>();
    }

    public class EmployeeEditViewModel
    {
        public Employee Employee { get; set; } = new Employee();
        public IEnumerable<Office> Offices { get; set; } = new List<Office>();
        public IEnumerable<MapLocation> MapLocations { get; set; } = new List<MapLocation>();
    }

    public class EmployeeDetailsViewModel
    {
        public Employee Employee { get; set; } = new Employee();
        public IEnumerable<EmployeeRecord> Records { get; set; } = new List<EmployeeRecord>();
        
        // Statistics
        public int TotalRecords { get; set; }
        public int PromotionsCount { get; set; }
        public int TransfersCount { get; set; }
        public int AppreciationsCount { get; set; }
        public int PenaltiesCount { get; set; }
        public int LeavesCount { get; set; }
    }

    public class EmployeeStatisticsViewModel
    {
        public int TotalEmployees { get; set; }
        public int OfficersCount { get; set; }
        public int NCOsCount { get; set; }
        public int PersonnelCount { get; set; }
        public int CivilEmployeesCount { get; set; }
        
        public decimal OfficersPercentage => TotalEmployees > 0 ? (decimal)OfficersCount / TotalEmployees * 100 : 0;
        public decimal NCOsPercentage => TotalEmployees > 0 ? (decimal)NCOsCount / TotalEmployees * 100 : 0;
        public decimal PersonnelPercentage => TotalEmployees > 0 ? (decimal)PersonnelCount / TotalEmployees * 100 : 0;
        public decimal CivilEmployeesPercentage => TotalEmployees > 0 ? (decimal)CivilEmployeesCount / TotalEmployees * 100 : 0;
    }

    public class EmployeeSearchViewModel
    {
        [Display(Name = "البحث")]
        public string? SearchTerm { get; set; }
        
        [Display(Name = "نوع المنتسب")]
        public EmployeeType? EmployeeType { get; set; }
        
        [Display(Name = "المكتب")]
        public string? OfficeId { get; set; }
        
        [Display(Name = "الرتبة")]
        public string? Rank { get; set; }
        
        [Display(Name = "القسم")]
        public string? Department { get; set; }
        
        [Display(Name = "من تاريخ التوظيف")]
        public DateTime? HireDateFrom { get; set; }
        
        [Display(Name = "إلى تاريخ التوظيف")]
        public DateTime? HireDateTo { get; set; }
        
        [Display(Name = "الحالة")]
        public bool? IsActive { get; set; }
    }

    public class EmployeeQuickAddViewModel
    {
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [Display(Name = "الاسم الكامل")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الرقم الوطني مطلوب")]
        [Display(Name = "الرقم الوطني")]
        public string NationalId { get; set; } = string.Empty;

        [Display(Name = "نوع المنتسب")]
        public EmployeeType EmployeeType { get; set; }

        [Display(Name = "الرتبة")]
        public string? Rank { get; set; }

        [Display(Name = "المنصب")]
        public string? Position { get; set; }

        [Display(Name = "المكتب")]
        public string? OfficeId { get; set; }

        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [Display(Name = "البريد الإلكتروني")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string? Email { get; set; }
    }

    public class EmployeeBulkActionViewModel
    {
        public IEnumerable<int> SelectedEmployeeIds { get; set; } = new List<int>();
        public string Action { get; set; } = string.Empty;
        public string? NewOfficeId { get; set; }
        public string? NewDepartment { get; set; }
        public bool? NewActiveStatus { get; set; }
    }

    public class EmployeeReportViewModel
    {
        public string ReportType { get; set; } = string.Empty;
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public EmployeeType? EmployeeType { get; set; }
        public string? OfficeId { get; set; }
        public string? Department { get; set; }
        public bool IncludeInactive { get; set; }
        public string ExportFormat { get; set; } = "PDF"; // PDF, Excel, CSV
    }
}
