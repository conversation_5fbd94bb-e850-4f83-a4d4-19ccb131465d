@model ERPSystem.Models.ViewModels.EmployeeStatisticsViewModel
@{
    ViewData["Title"] = "إحصائيات المنتسبين";
}

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="page-title">
                    <i class="fas fa-chart-bar text-primary"></i>
                    إحصائيات المنتسبين
                </h2>
                <div class="page-actions">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card total">
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">@Model.TotalEmployees</h3>
                    <p class="stats-label">إجمالي المنتسبين</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card officers">
                <div class="stats-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">@Model.OfficersCount</h3>
                    <p class="stats-label">الضباط</p>
                    <small class="stats-percentage">@Model.OfficersPercentage.ToString("F1")%</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card ncos">
                <div class="stats-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">@Model.NCOsCount</h3>
                    <p class="stats-label">ضباط الصف</p>
                    <small class="stats-percentage">@Model.NCOsPercentage.ToString("F1")%</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card personnel">
                <div class="stats-icon">
                    <i class="fas fa-user-friends"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">@Model.PersonnelCount</h3>
                    <p class="stats-label">الأفراد</p>
                    <small class="stats-percentage">@Model.PersonnelPercentage.ToString("F1")%</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stats-card employees">
                <div class="stats-icon">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div class="stats-content">
                    <h3 class="stats-number">@Model.CivilEmployeesCount</h3>
                    <p class="stats-label">الموظفون المدنيون</p>
                    <small class="stats-percentage">@Model.CivilEmployeesPercentage.ToString("F1")%</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-pie-chart"></i>
                        توزيع المنتسبين حسب النوع
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="employeeTypeChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bar-chart"></i>
                        إحصائيات مقارنة
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="comparisonChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table"></i>
                        تفاصيل الإحصائيات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>نوع المنتسب</th>
                                    <th>العدد</th>
                                    <th>النسبة المئوية</th>
                                    <th>التمثيل البياني</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">الضباط</span>
                                    </td>
                                    <td>@Model.OfficersCount</td>
                                    <td>@Model.OfficersPercentage.ToString("F1")%</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-primary" role="progressbar" 
                                                 style="width: @Model.OfficersPercentage.ToString("F1")%">
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge bg-success">ضباط الصف</span>
                                    </td>
                                    <td>@Model.NCOsCount</td>
                                    <td>@Model.NCOsPercentage.ToString("F1")%</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" role="progressbar" 
                                                 style="width: @Model.NCOsPercentage.ToString("F1")%">
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge bg-info">الأفراد</span>
                                    </td>
                                    <td>@Model.PersonnelCount</td>
                                    <td>@Model.PersonnelPercentage.ToString("F1")%</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-info" role="progressbar" 
                                                 style="width: @Model.PersonnelPercentage.ToString("F1")%">
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="badge bg-warning">الموظفون المدنيون</span>
                                    </td>
                                    <td>@Model.CivilEmployeesCount</td>
                                    <td>@Model.CivilEmployeesPercentage.ToString("F1")%</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-warning" role="progressbar" 
                                                 style="width: @Model.CivilEmployeesPercentage.ToString("F1")%">
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="table-primary">
                                    <td><strong>الإجمالي</strong></td>
                                    <td><strong>@Model.TotalEmployees</strong></td>
                                    <td><strong>100.0%</strong></td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-primary" role="progressbar" style="width: 100%">
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Pie Chart for Employee Types
        const pieCtx = document.getElementById('employeeTypeChart').getContext('2d');
        const pieChart = new Chart(pieCtx, {
            type: 'pie',
            data: {
                labels: ['الضباط', 'ضباط الصف', 'الأفراد', 'الموظفون المدنيون'],
                datasets: [{
                    data: [@Model.OfficersCount, @Model.NCOsCount, @Model.PersonnelCount, @Model.CivilEmployeesCount],
                    backgroundColor: [
                        '#1e40af',
                        '#10b981',
                        '#0ea5e9',
                        '#f59e0b'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // Bar Chart for Comparison
        const barCtx = document.getElementById('comparisonChart').getContext('2d');
        const barChart = new Chart(barCtx, {
            type: 'bar',
            data: {
                labels: ['الضباط', 'ضباط الصف', 'الأفراد', 'الموظفون المدنيون'],
                datasets: [{
                    label: 'عدد المنتسبين',
                    data: [@Model.OfficersCount, @Model.NCOsCount, @Model.PersonnelCount, @Model.CivilEmployeesCount],
                    backgroundColor: [
                        'rgba(30, 64, 175, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(14, 165, 233, 0.8)',
                        'rgba(245, 158, 11, 0.8)'
                    ],
                    borderColor: [
                        '#1e40af',
                        '#10b981',
                        '#0ea5e9',
                        '#f59e0b'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    </script>
}

@section Styles {
    <style>
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1e40af, #3b82f6);
        }

        .stats-card.officers::before {
            background: linear-gradient(90deg, #1e40af, #3b82f6);
        }

        .stats-card.ncos::before {
            background: linear-gradient(90deg, #10b981, #34d399);
        }

        .stats-card.personnel::before {
            background: linear-gradient(90deg, #0ea5e9, #38bdf8);
        }

        .stats-card.employees::before {
            background: linear-gradient(90deg, #f59e0b, #fbbf24);
        }

        .stats-card.total::before {
            background: linear-gradient(90deg, #6366f1, #8b5cf6);
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
        }

        .stats-card.officers .stats-icon {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
        }

        .stats-card.ncos .stats-icon {
            background: linear-gradient(135deg, #10b981, #34d399);
        }

        .stats-card.personnel .stats-icon {
            background: linear-gradient(135deg, #0ea5e9, #38bdf8);
        }

        .stats-card.employees .stats-icon {
            background: linear-gradient(135deg, #f59e0b, #fbbf24);
        }

        .stats-card.total .stats-icon {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 900;
            color: #1e293b;
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stats-label {
            font-size: 1rem;
            color: #64748b;
            margin-bottom: 0.25rem;
            font-weight: 600;
        }

        .stats-percentage {
            color: #10b981;
            font-weight: 600;
        }

        .page-title {
            color: #1e40af;
            font-weight: 700;
        }

        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
            border-radius: 12px 12px 0 0 !important;
        }

        .progress {
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-bar {
            border-radius: 10px;
        }

        @@media print {
            .page-actions {
                display: none !important;
            }
            
            .stats-card {
                break-inside: avoid;
                margin-bottom: 1rem;
            }
        }
    </style>
}
