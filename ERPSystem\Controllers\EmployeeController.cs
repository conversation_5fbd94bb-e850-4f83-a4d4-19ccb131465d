using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ERPSystem.Data;
using ERPSystem.Models.Entities;
using ERPSystem.Models.ViewModels;

namespace ERPSystem.Controllers
{
    [Authorize]
    public class EmployeeController : Controller
    {
        private readonly ApplicationDbContext _context;

        public EmployeeController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Employee
        public async Task<IActionResult> Index(string? search, EmployeeType? employeeType, string? office, int page = 1, int pageSize = 10)
        {
            var query = _context.Employees
                .Include(e => e.Office)
                .Include(e => e.MapLocation)
                .AsQueryable();

            // Search filter
            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(e => 
                    e.FullName.Contains(search) ||
                    e.NationalId.Contains(search) ||
                    (e.EmployeeNumber != null && e.EmployeeNumber.Contains(search)) ||
                    (e.MilitaryNumber != null && e.MilitaryNumber.Contains(search)));
            }

            // Employee type filter
            if (employeeType.HasValue)
            {
                query = query.Where(e => e.EmployeeType == employeeType.Value);
            }

            // Office filter
            if (!string.IsNullOrEmpty(office))
            {
                query = query.Where(e => e.OfficeId == office);
            }

            // Only active employees by default
            query = query.Where(e => e.IsActive);

            // Order by creation date (newest first)
            query = query.OrderByDescending(e => e.CreatedDate);

            // Pagination
            var totalCount = await query.CountAsync();
            var employees = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Get offices for filter dropdown
            var offices = await _context.Offices
                .Where(o => o.IsActive)
                .OrderBy(o => o.Name)
                .ToListAsync();

            var viewModel = new EmployeeIndexViewModel
            {
                Employees = employees,
                Offices = offices,
                CurrentSearch = search,
                CurrentEmployeeType = employeeType,
                CurrentOffice = office,
                CurrentPage = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return View(viewModel);
        }

        // GET: Employee/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var employee = await _context.Employees
                .Include(e => e.Office)
                .Include(e => e.MapLocation)
                .Include(e => e.Records)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (employee == null)
            {
                return NotFound();
            }

            return View(employee);
        }

        // GET: Employee/Create
        public async Task<IActionResult> Create()
        {
            var offices = await _context.Offices
                .Where(o => o.IsActive)
                .OrderBy(o => o.Name)
                .ToListAsync();

            var mapLocations = await _context.MapLocations
                .OrderBy(m => m.Name)
                .ToListAsync();

            var viewModel = new EmployeeCreateViewModel
            {
                Employee = new Employee(),
                Offices = offices,
                MapLocations = mapLocations
            };

            return View(viewModel);
        }

        // POST: Employee/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(EmployeeCreateViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                // Check for duplicate National ID
                var existingEmployee = await _context.Employees
                    .FirstOrDefaultAsync(e => e.NationalId == viewModel.Employee.NationalId);

                if (existingEmployee != null)
                {
                    ModelState.AddModelError("Employee.NationalId", "الرقم الوطني موجود مسبقاً");
                }
                else
                {
                    viewModel.Employee.CreatedDate = DateTime.Now;
                    _context.Add(viewModel.Employee);
                    await _context.SaveChangesAsync();

                    TempData["SuccessMessage"] = "تم إضافة المنتسب بنجاح";
                    return RedirectToAction(nameof(Index));
                }
            }

            // Reload dropdown data if validation fails
            viewModel.Offices = await _context.Offices
                .Where(o => o.IsActive)
                .OrderBy(o => o.Name)
                .ToListAsync();

            viewModel.MapLocations = await _context.MapLocations
                .OrderBy(m => m.Name)
                .ToListAsync();

            return View(viewModel);
        }

        // GET: Employee/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var employee = await _context.Employees.FindAsync(id);
            if (employee == null)
            {
                return NotFound();
            }

            var offices = await _context.Offices
                .Where(o => o.IsActive)
                .OrderBy(o => o.Name)
                .ToListAsync();

            var mapLocations = await _context.MapLocations
                .OrderBy(m => m.Name)
                .ToListAsync();

            var viewModel = new EmployeeEditViewModel
            {
                Employee = employee,
                Offices = offices,
                MapLocations = mapLocations
            };

            return View(viewModel);
        }

        // POST: Employee/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, EmployeeEditViewModel viewModel)
        {
            if (id != viewModel.Employee.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Check for duplicate National ID (excluding current employee)
                    var existingEmployee = await _context.Employees
                        .FirstOrDefaultAsync(e => e.NationalId == viewModel.Employee.NationalId && e.Id != id);

                    if (existingEmployee != null)
                    {
                        ModelState.AddModelError("Employee.NationalId", "الرقم الوطني موجود مسبقاً");
                    }
                    else
                    {
                        viewModel.Employee.UpdatedDate = DateTime.Now;
                        _context.Update(viewModel.Employee);
                        await _context.SaveChangesAsync();

                        TempData["SuccessMessage"] = "تم تحديث بيانات المنتسب بنجاح";
                        return RedirectToAction(nameof(Index));
                    }
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!EmployeeExists(viewModel.Employee.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            // Reload dropdown data if validation fails
            viewModel.Offices = await _context.Offices
                .Where(o => o.IsActive)
                .OrderBy(o => o.Name)
                .ToListAsync();

            viewModel.MapLocations = await _context.MapLocations
                .OrderBy(m => m.Name)
                .ToListAsync();

            return View(viewModel);
        }

        // POST: Employee/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var employee = await _context.Employees.FindAsync(id);
            if (employee != null)
            {
                // Soft delete - just mark as inactive
                employee.IsActive = false;
                employee.UpdatedDate = DateTime.Now;
                _context.Update(employee);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "تم حذف المنتسب بنجاح";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Employee/Statistics
        public async Task<IActionResult> Statistics()
        {
            var stats = new EmployeeStatisticsViewModel
            {
                TotalEmployees = await _context.Employees.CountAsync(e => e.IsActive),
                OfficersCount = await _context.Employees.CountAsync(e => e.IsActive && e.EmployeeType == EmployeeType.Officer),
                NCOsCount = await _context.Employees.CountAsync(e => e.IsActive && e.EmployeeType == EmployeeType.NCO),
                PersonnelCount = await _context.Employees.CountAsync(e => e.IsActive && e.EmployeeType == EmployeeType.Personnel),
                CivilEmployeesCount = await _context.Employees.CountAsync(e => e.IsActive && e.EmployeeType == EmployeeType.Employee)
            };

            return View(stats);
        }

        private bool EmployeeExists(int id)
        {
            return _context.Employees.Any(e => e.Id == id);
        }
    }
}
